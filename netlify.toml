[build]
  base = "frontend"
  publish = "build"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  REACT_APP_API_BASE_URL = "https://stripphotobooth.onrender.com"

[context.deploy-preview.environment]
  REACT_APP_API_BASE_URL = "https://stripphotobooth.onrender.com"

[context.branch-deploy.environment]
  REACT_APP_API_BASE_URL = "https://stripphotobooth.onrender.com"
