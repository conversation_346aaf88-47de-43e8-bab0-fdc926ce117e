{"name": "strip-photobooth", "version": "1.0.0", "description": "A modern photo booth application that creates beautiful 2×6 inch photo strips with custom templates", "main": "index.js", "scripts": {"install-all": "npm install && cd frontend && npm install && cd ../backend && npm install", "dev": "concurrently \"npm run backend\" \"npm run frontend\"", "backend": "cd backend && npm run dev", "frontend": "cd frontend && npm start", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["photobooth", "photo-strip", "react", "nodejs", "mongodb", "cloudinary", "event-photography", "printing"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/strip-photobooth.git"}, "bugs": {"url": "https://github.com/yourusername/strip-photobooth/issues"}, "homepage": "https://github.com/yourusername/strip-photobooth#readme", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}