# Server Configuration
PORT=5000
NODE_ENV=production

# MongoDB Configuration
MONGO_URI=mongodb+srv://digambarkothawale05:<EMAIL>/

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=dfhelz5am
CLOUDINARY_API_KEY=685936334853159
CLOUDINARY_API_SECRET=ifOyel7-dC_ZmK9S-rRPKsH7ZnU

# CORS Configuration
# Update this with your Netlify URL after deployment
FRONTEND_URL=https://your-netlify-app.netlify.app

# Security
JWT_SECRET=your_jwt_secret_key_here_change_in_production
