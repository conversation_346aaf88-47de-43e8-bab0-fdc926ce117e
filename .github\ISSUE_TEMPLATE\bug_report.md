---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
 - Browser: [e.g. Chrome 96, Firefox 95, Safari 15]
 - Device: [e.g. Desktop, iPhone 12, iPad Pro]
 - Node.js version: [e.g. 16.14.0]
 - Application version: [e.g. 1.0.0]

**Console Errors**
If applicable, paste any console errors here:
```
Paste console errors here
```

**Additional context**
Add any other context about the problem here.

**Possible Solution**
If you have ideas on how to fix this, please describe them here.
