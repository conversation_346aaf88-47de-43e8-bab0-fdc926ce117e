/* Mobile Camera Optimization Styles */

/* Mobile-first responsive design */
@media (max-width: 640px) {
  .mobile-camera-container {
    margin: 0 !important;
    padding: 8px !important;
    border-radius: 12px !important;
    width: calc(100vw - 16px) !important;
    max-width: none !important;
  }
  
  .mobile-camera-video {
    min-height: 50vh !important;
    max-height: 60vh !important;
    object-fit: cover !important;
    width: 100% !important;
    border-radius: 8px !important;
  }
  
  .mobile-header {
    padding: 12px !important;
    margin-bottom: 16px !important;
  }
  
  .mobile-header h1 {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }
  
  .mobile-controls {
    padding: 12px 8px !important;
    gap: 12px !important;
  }
  
  .mobile-button {
    padding: 16px 12px !important;
    font-size: 1.1rem !important;
    border-radius: 12px !important;
    min-height: 56px !important;
  }
  
  .mobile-camera-switch {
    padding: 12px !important;
    font-size: 0.95rem !important;
    min-height: 48px !important;
  }
}

/* Tablet optimization */
@media (min-width: 641px) and (max-width: 1024px) {
  .mobile-camera-video {
    min-height: 40vh !important;
    max-height: 55vh !important;
  }
}

/* Landscape mobile optimization */
@media (max-width: 640px) and (orientation: landscape) {
  .mobile-camera-video {
    min-height: 70vh !important;
    max-height: 80vh !important;
  }
  
  .mobile-header {
    margin-bottom: 8px !important;
  }
  
  .mobile-header h1 {
    font-size: 1.5rem !important;
  }
}

/* iPhone specific optimizations */
@media (max-width: 414px) {
  .mobile-camera-container {
    padding: 6px !important;
    width: calc(100vw - 12px) !important;
  }
  
  .mobile-camera-video {
    min-height: 55vh !important;
    border-radius: 6px !important;
  }
}

/* Android specific optimizations */
@media (max-width: 640px) and (-webkit-min-device-pixel-ratio: 2) {
  .mobile-camera-video {
    transform: scale(1.01); /* Slight scale to fill any gaps */
  }
}

/* Full-screen camera mode for very small screens */
@media (max-width: 375px) {
  .mobile-camera-container {
    padding: 4px !important;
    width: calc(100vw - 8px) !important;
    border-radius: 8px !important;
  }
  
  .mobile-camera-video {
    min-height: 60vh !important;
    max-height: 65vh !important;
  }
  
  .mobile-button {
    font-size: 1rem !important;
    padding: 14px 10px !important;
  }
}
