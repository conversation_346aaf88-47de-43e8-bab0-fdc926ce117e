services:
  - type: web
    name: strip-photobooth-backend
    env: node
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    plan: free
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 5000
      - key: MONGO_URI
        value: mongodb+srv://digambarkothawale05:<EMAIL>/
      - key: CLOUDINARY_CLOUD_NAME
        value: dfhelz5am
      - key: CLOUDINARY_API_KEY
        value: 685936334853159
      - key: CLOUDINARY_API_SECRET
        value: ifOyel7-dC_ZmK9S-rRPKsH7ZnU
      - key: FRONTEND_URL
        value: https://68680d4b0e65254ad58b8b9e--strippphotobooth.netlify.app
      - key: JWT_SECRET
        value: your_secure_jwt_secret_change_this_in_production
