@tailwind base;
@tailwind components;
@tailwind utilities;

/* Essential responsive styles for Strip Photobooth */

/* Touch-friendly button styles */
.touch-button {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  user-select: none;
}

/* Prevent zoom on input focus for iOS */
@media screen and (max-width: 768px) {
  input, select, textarea {
    font-size: 16px !important;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent overscroll bounce on iOS */
body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* Better focus states for accessibility */
button:focus-visible,
input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Touch-friendly button sizing for mobile */
@media (max-width: 640px) {
  button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Safe area support for notched devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}
